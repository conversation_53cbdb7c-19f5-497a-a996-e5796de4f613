# SeaTunnel SFTP连接池修复后的测试配置
# 使用更宽松的连接池配置避免初始化阶段的连接耗尽

env {
  execution.parallelism = 1  # 先用单并发测试
  parallelism = 1
  job.mode = "BATCH"
  checkpoint.interval = 10000
}

source {
  Jdbc {
    driver = "com.mysql.cj.jdbc.Driver"
    url = "*******************************************************************"
    user = "test_user"
    password = "test_password"
    
    # 简单的测试查询
    query = """
      SELECT 
        id,
        name,
        email,
        created_at
      FROM test_table 
      LIMIT 100
    """
  }
}

sink {
  SftpFile {
    host = "your-sftp-host"
    port = 22
    user = "your-sftp-user"
    password = "your-sftp-password"
    
    path = "/sftp/test/output"
    file_name_expression = "${transactionId}_${now}"
    file_format_type = "csv"
    
    # 修复后的SFTP连接池配置 - 增加连接数
    hadoop_conf = {
      "fs.sftp.connection.max" = "10"  # 增加到10个连接
      "fs.sftp.impl" = "org.apache.seatunnel.connectors.seatunnel.file.sftp.system.SFTPFileSystem"
      "fs.sftp.timeout" = "60000"
      "fs.sftp.buffer.size" = "32768"
    }
    
    # 文件写入配置
    row_delimiter = "\n"
    field_delimiter = ","
    
    # 启用事务
    is_enable_transaction = true
    
    # CSV配置
    csv {
      with_header = true
    }
    
    # 保存模式配置
    save_mode = "APPEND"
  }
}
