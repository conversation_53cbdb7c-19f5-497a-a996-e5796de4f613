#!/bin/bash

# 简单的编译测试脚本
echo "=== SeaTunnel SFTP连接器编译测试 ==="

# 检查Maven是否可用
if ! command -v mvn &> /dev/null; then
    echo "❌ Maven未安装，无法进行编译测试"
    exit 1
fi

# 进入SFTP连接器目录
SFTP_DIR="seatunnel-connectors-v2/connector-file/connector-file-sftp"
if [ ! -d "$SFTP_DIR" ]; then
    echo "❌ 找不到SFTP连接器目录: $SFTP_DIR"
    exit 1
fi

echo "📁 进入目录: $SFTP_DIR"
cd "$SFTP_DIR"

# 编译测试
echo "🔨 开始编译测试..."
if mvn clean compile -q; then
    echo "✅ SFTP连接器编译成功!"
else
    echo "❌ SFTP连接器编译失败!"
    exit 1
fi

echo ""
echo "🎯 编译测试完成!"
echo "✅ 修复的SFTP连接池代码编译通过"
