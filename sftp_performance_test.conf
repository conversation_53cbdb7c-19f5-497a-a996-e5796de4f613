# SFTP连接池性能测试配置
# 用于测试修复后的连接池在高并发下的表现

env {
  execution.parallelism = 4  # 增加并发度测试
  parallelism = 4
  job.mode = "BATCH"
  checkpoint.interval = 5000
  
  # JVM优化参数
  java.opts = "-Xmx2g -Xms1g -XX:+UseG1GC"
}

source {
  # 使用FakeSource生成测试数据，避免数据库依赖
  FakeSource {
    parallelism = 4
    result_table_name = "fake_table"
    row_num = 10000  # 生成10000行测试数据
    schema = {
      fields {
        id = "int"
        name = "string"
        email = "string"
        age = "int"
        salary = "double"
        department = "string"
        created_at = "timestamp"
        description = "string"
      }
    }
    
    # 数据生成规则
    rows = [
      {
        kind = INTERVAL
        fields = [
          {name = "id", kind = SEQUENCE, start = 1, end = 10000}
          {name = "name", kind = RANDOM, options = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}
          {name = "email", kind = TEMPLATE, template = "${name.toLowerCase()}@company.com"}
          {name = "age", kind = RANDOM_INT, min = 22, max = 65}
          {name = "salary", kind = RANDOM_DOUBLE, min = 30000.0, max = 150000.0}
          {name = "department", kind = RANDOM, options = ["IT", "HR", "Finance", "Marketing", "Sales", "Operations"]}
          {name = "created_at", kind = NOW}
          {name = "description", kind = TEMPLATE, template = "Employee ${id} in ${department} department with ${age} years experience"}
        ]
      }
    ]
  }
}

transform {
  # 添加数据处理以模拟真实场景
  Sql {
    sql = """
      SELECT 
        id,
        name,
        email,
        age,
        salary,
        department,
        created_at,
        description,
        CASE 
          WHEN salary > 100000 THEN 'Senior'
          WHEN salary > 60000 THEN 'Mid'
          ELSE 'Junior'
        END as level,
        CONCAT(department, '_', level) as dept_level,
        CURRENT_TIMESTAMP() as processed_at
      FROM fake_table
    """
  }
}

sink {
  SftpFile {
    host = "your-sftp-host"
    port = 22
    user = "your-sftp-user"
    password = "your-sftp-password"
    
    path = "/sftp/performance_test"
    file_name_expression = "perf_test_${transactionId}_${now}"
    file_format_type = "json"
    
    # 优化的连接池配置
    hadoop_conf = {
      "fs.sftp.connection.max" = "5"  # 适当增加连接数
      "fs.sftp.impl" = "org.apache.seatunnel.connectors.seatunnel.file.sftp.system.SFTPFileSystem"
      "fs.sftp.timeout" = "60000"
      "fs.sftp.buffer.size" = "65536"  # 64KB缓冲区
      "fs.sftp.block.size" = "8192"    # 8KB块大小
    }
    
    # 批处理配置
    batch_size = 1000
    
    # 启用事务
    is_enable_transaction = true
    
    # 文件压缩
    compress_codec = "gzip"
    
    # JSON配置
    json {
      enable_complex_format = true
    }
  }
}
