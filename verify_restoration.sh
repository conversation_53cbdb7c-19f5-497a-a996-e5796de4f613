#!/bin/bash

echo "=== 验证代码还原状态 ==="

# 检查SFTPConnectionPool的关键修复
echo "1. 检查SFTPConnectionPool关键修复..."

if grep -q "it.remove()" seatunnel-connectors-v2/connector-file/connector-file-sftp/src/main/java/org/apache/seatunnel/connectors/seatunnel/file/sftp/system/SFTPConnectionPool.java; then
    echo "   ✓ 关键bug修复保留: it.remove()"
else
    echo "   ✗ 关键bug修复丢失"
fi

if grep -q "AtomicInteger" seatunnel-connectors-v2/connector-file/connector-file-sftp/src/main/java/org/apache/seatunnel/connectors/seatunnel/file/sftp/system/SFTPConnectionPool.java; then
    echo "   ✓ 线程安全改进保留: AtomicInteger"
else
    echo "   ✗ 线程安全改进丢失"
fi

if grep -q "ConcurrentHashMap" seatunnel-connectors-v2/connector-file/connector-file-sftp/src/main/java/org/apache/seatunnel/connectors/seatunnel/file/sftp/system/SFTPConnectionPool.java; then
    echo "   ✓ 线程安全改进保留: ConcurrentHashMap"
else
    echo "   ✗ 线程安全改进丢失"
fi

# 检查是否移除了复杂的超时配置
if ! grep -q "ServerAliveInterval" seatunnel-connectors-v2/connector-file/connector-file-sftp/src/main/java/org/apache/seatunnel/connectors/seatunnel/file/sftp/system/SFTPConnectionPool.java; then
    echo "   ✓ 复杂网络配置已移除"
else
    echo "   ✗ 复杂网络配置仍存在"
fi

# 检查SFTPFileSystem
echo ""
echo "2. 检查SFTPFileSystem..."

if ! grep -q "TimeoutSFTPOutputStream\|InterruptibleSFTPOutputStream" seatunnel-connectors-v2/connector-file/connector-file-sftp/src/main/java/org/apache/seatunnel/connectors/seatunnel/file/sftp/system/SFTPFileSystem.java; then
    echo "   ✓ 超时包装器已移除"
else
    echo "   ✗ 超时包装器仍存在"
fi

if grep -q "DEFAULT_MAX_CONNECTION = 20" seatunnel-connectors-v2/connector-file/connector-file-sftp/src/main/java/org/apache/seatunnel/connectors/seatunnel/file/sftp/system/SFTPFileSystem.java; then
    echo "   ✓ 默认连接数已还原: 20"
else
    echo "   ✗ 默认连接数未还原"
fi

# 检查TextWriteStrategy
echo ""
echo "3. 检查TextWriteStrategy..."

if ! grep -q "CompletableFuture\|WRITE_TIMEOUT" seatunnel-connectors-v2/connector-file/connector-file-base/src/main/java/org/apache/seatunnel/connectors/seatunnel/file/sink/writer/TextWriteStrategy.java; then
    echo "   ✓ 超时机制已移除"
else
    echo "   ✗ 超时机制仍存在"
fi

if ! grep -q "@Slf4j" seatunnel-connectors-v2/connector-file/connector-file-base/src/main/java/org/apache/seatunnel/connectors/seatunnel/file/sink/writer/TextWriteStrategy.java; then
    echo "   ✓ 不必要的注解已移除"
else
    echo "   ✗ 不必要的注解仍存在"
fi

# 检查是否删除了超时相关文件
echo ""
echo "4. 检查超时相关文件..."

if [ ! -f "seatunnel-connectors-v2/connector-file/connector-file-sftp/src/main/java/org/apache/seatunnel/connectors/seatunnel/file/sftp/system/TimeoutSFTPOutputStream.java" ]; then
    echo "   ✓ TimeoutSFTPOutputStream.java 已删除"
else
    echo "   ✗ TimeoutSFTPOutputStream.java 仍存在"
fi

if [ ! -f "seatunnel-connectors-v2/connector-file/connector-file-sftp/src/main/java/org/apache/seatunnel/connectors/seatunnel/file/sftp/system/InterruptibleSFTPOutputStream.java" ]; then
    echo "   ✓ InterruptibleSFTPOutputStream.java 已删除"
else
    echo "   ✗ InterruptibleSFTPOutputStream.java 仍存在"
fi

echo ""
echo "=== 还原状态总结 ==="
echo ""
echo "✅ 保留的关键修复:"
echo "   - SFTP连接池的关键bug修复 (getFromPool方法)"
echo "   - 线程安全改进 (AtomicInteger, ConcurrentHashMap)"
echo "   - 连接有效性验证"
echo ""
echo "❌ 移除的复杂功能:"
echo "   - 超时包装器和看门狗机制"
echo "   - 复杂的网络配置"
echo "   - Socket级别的超时设置"
echo ""
echo "🎯 当前状态:"
echo "   代码已还原到线程池优化后的干净状态"
echo "   保留了最重要的bug修复，移除了可能导致问题的复杂机制"
echo ""
echo "🚀 建议测试:"
echo "   mvn compile -pl seatunnel-connectors-v2/connector-file/connector-file-sftp -am"
echo "   然后使用保守的配置进行测试"
