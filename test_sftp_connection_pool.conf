# SeaTunnel SFTP连接池测试配置
# 用于验证修复后的SFTP连接池在并发情况下的稳定性

env {
  execution.parallelism = 2
  parallelism = 2
  job.mode = "BATCH"
  checkpoint.interval = 10000
}

source {
  Jdbc {
    driver = "com.mysql.cj.jdbc.Driver"
    url = "*******************************************************************"
    user = "test_user"
    password = "test_password"
    
    # 测试查询 - 生成一些测试数据
    query = """
      SELECT 
        id,
        name,
        email,
        created_at,
        CONCAT('test_data_', id) as test_field
      FROM test_table 
      WHERE id BETWEEN 1 AND 1000
    """
    
    # 分区配置以测试并发
    partition_column = "id"
    partition_lower_bound = 1
    partition_upper_bound = 1000
    partition_num = 4
  }
}

transform {
  # 添加一些转换以增加处理时间，测试连接池稳定性
  Sql {
    sql = """
      SELECT 
        id,
        name,
        email,
        created_at,
        test_field,
        CURRENT_TIMESTAMP() as processed_at
      FROM source
    """
  }
}

sink {
  SftpFile {
    host = "your-sftp-host"
    port = 22
    user = "your-sftp-user"
    password = "your-sftp-password"
    
    path = "/sftp/test/output"
    file_name_expression = "${transactionId}_${now}"
    file_format_type = "csv"
    
    # 优化的SFTP连接池配置
    hadoop_conf = {
      "fs.sftp.connection.max" = "3"  # 限制连接数，避免过载
      "fs.sftp.impl" = "org.apache.seatunnel.connectors.seatunnel.file.sftp.system.SFTPFileSystem"
      "fs.sftp.timeout" = "60000"     # 60秒超时
      "fs.sftp.buffer.size" = "32768" # 32KB缓冲区
    }
    
    # 文件写入配置
    row_delimiter = "\n"
    field_delimiter = ","
    
    # 启用事务以确保数据一致性
    is_enable_transaction = true
    
    # 文件压缩
    compress_codec = "gzip"
    
    # CSV配置
    csv {
      with_header = true
    }
  }
}
