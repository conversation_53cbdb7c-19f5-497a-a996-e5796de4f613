#!/bin/bash

# SFTP连接池修复验证脚本
# 用于验证修复后的SFTP连接池是否正常工作

echo "=== SeaTunnel SFTP连接池修复验证 ==="
echo "开始时间: $(date)"
echo ""

# 检查文件是否存在
SFTP_POOL_FILE="seatunnel-connectors-v2/connector-file/connector-file-sftp/src/main/java/org/apache/seatunnel/connectors/seatunnel/file/sftp/system/SFTPConnectionPool.java"

if [ ! -f "$SFTP_POOL_FILE" ]; then
    echo "❌ 错误: 找不到SFTPConnectionPool.java文件"
    exit 1
fi

echo "📁 检查文件: $SFTP_POOL_FILE"
echo ""

# 1. 检查关键修复
echo "🔍 1. 检查关键bug修复..."
if grep -q "it.remove()" "$SFTP_POOL_FILE"; then
    echo "   ✅ 找到关键修复: it.remove() 而不是 idleConnections.remove(info)"
else
    echo "   ❌ 未找到关键修复"
    exit 1
fi

# 2. 检查线程安全改进
echo ""
echo "🔍 2. 检查线程安全改进..."
if grep -q "AtomicInteger" "$SFTP_POOL_FILE"; then
    echo "   ✅ 找到AtomicInteger改进"
else
    echo "   ❌ 未找到AtomicInteger改进"
fi

if grep -q "ConcurrentHashMap" "$SFTP_POOL_FILE"; then
    echo "   ✅ 找到ConcurrentHashMap改进"
else
    echo "   ❌ 未找到ConcurrentHashMap改进"
fi

# 3. 检查连接验证
echo ""
echo "🔍 3. 检查连接验证机制..."
if grep -q "channel.isConnected()" "$SFTP_POOL_FILE"; then
    echo "   ✅ 找到连接验证机制"
else
    echo "   ❌ 未找到连接验证机制"
fi

# 4. 检查超时配置
echo ""
echo "🔍 4. 检查超时配置..."
if grep -q "ConnectTimeout" "$SFTP_POOL_FILE"; then
    echo "   ✅ 找到连接超时配置"
else
    echo "   ❌ 未找到连接超时配置"
fi

# 5. 检查连接数限制
echo ""
echo "🔍 5. 检查连接数限制..."
if grep -q "Maximum connection limit reached" "$SFTP_POOL_FILE"; then
    echo "   ✅ 找到连接数限制检查"
else
    echo "   ❌ 未找到连接数限制检查"
fi

# 6. 检查SFTPFileSystem初始化修复
echo ""
echo "🔍 6. 检查SFTPFileSystem初始化修复..."
SFTP_FILESYSTEM_FILE="seatunnel-connectors-v2/connector-file/connector-file-sftp/src/main/java/org/apache/seatunnel/connectors/seatunnel/file/sftp/system/SFTPFileSystem.java"
if grep -q "connectionMax, 0" "$SFTP_FILESYSTEM_FILE"; then
    echo "   ✅ 找到连接池初始化修复"
else
    echo "   ❌ 未找到连接池初始化修复"
fi

# 7. 统计修复内容
echo ""
echo "📊 修复内容统计:"
echo "   - 关键bug修复: $(grep -c "it.remove()" "$SFTP_POOL_FILE") 处"
echo "   - 线程安全改进: $(grep -c "AtomicInteger\|ConcurrentHashMap" "$SFTP_POOL_FILE") 处"
echo "   - 连接验证: $(grep -c "isConnected()" "$SFTP_POOL_FILE") 处"
echo "   - 日志记录: $(grep -c "LOG\." "$SFTP_POOL_FILE") 处"
echo "   - 初始化修复: $(grep -c "connectionMax, 0" "$SFTP_FILESYSTEM_FILE") 处"

echo ""
echo "🎯 修复验证完成!"
echo ""

# 7. 编译验证
echo "🔨 7. 编译验证..."
if command -v mvn &> /dev/null; then
    echo "   正在编译SFTP连接器..."
    cd seatunnel-connectors-v2/connector-file/connector-file-sftp
    if mvn clean compile -q; then
        echo "   ✅ 编译成功"
    else
        echo "   ❌ 编译失败"
        exit 1
    fi
    cd - > /dev/null
else
    echo "   ⚠️  Maven未安装，跳过编译验证"
fi

echo ""
echo "📋 下一步建议:"
echo "   1. 重新编译整个SeaTunnel项目"
echo "   2. 使用测试配置进行功能测试"
echo "   3. 监控日志确认不再出现连接池错误"
echo "   4. 逐步增加并发度进行压力测试"

echo ""
echo "🧪 测试命令:"
echo "   ./bin/seatunnel.sh --config test_sftp_connection_pool.conf"

echo ""
echo "✅ 验证完成! 时间: $(date)"
