#!/bin/bash

# SFTP连接池问题诊断脚本
echo "=== SeaTunnel SFTP连接池问题诊断 ==="
echo "时间: $(date)"
echo ""

# 1. 检查修复是否已应用
echo "🔍 1. 检查SFTP连接池修复状态..."
SFTP_FILE="seatunnel-connectors-v2/connector-file/connector-file-sftp/src/main/java/org/apache/seatunnel/connectors/seatunnel/file/sftp/system/SFTPConnectionPool.java"

if [ ! -f "$SFTP_FILE" ]; then
    echo "❌ 找不到SFTPConnectionPool.java文件"
    exit 1
fi

# 检查关键修复
if grep -q "it.remove()" "$SFTP_FILE"; then
    echo "   ✅ 核心bug已修复"
else
    echo "   ❌ 核心bug未修复"
fi

if grep -q "AtomicInteger" "$SFTP_FILE"; then
    echo "   ✅ 线程安全改进已应用"
else
    echo "   ❌ 线程安全改进未应用"
fi

# 2. 分析错误日志
echo ""
echo "🔍 2. 分析最近的错误日志..."
LOG_DIR="logs"
if [ -d "$LOG_DIR" ]; then
    echo "   检查日志目录: $LOG_DIR"
    
    # 查找SFTP相关错误
    if find "$LOG_DIR" -name "*.log" -type f -exec grep -l "SFTP connection pool exhausted" {} \; 2>/dev/null | head -1; then
        echo "   ❌ 发现连接池耗尽错误"
        echo "   最近的错误:"
        find "$LOG_DIR" -name "*.log" -type f -exec grep -h "SFTP connection pool exhausted" {} \; 2>/dev/null | tail -3
    else
        echo "   ✅ 未发现连接池耗尽错误"
    fi
    
    # 查找其他SFTP错误
    if find "$LOG_DIR" -name "*.log" -type f -exec grep -l "FileNotFoundException" {} \; 2>/dev/null | head -1; then
        echo "   ⚠️  发现FileNotFoundException错误"
        echo "   最近的错误:"
        find "$LOG_DIR" -name "*.log" -type f -exec grep -h "FileNotFoundException" {} \; 2>/dev/null | tail -3
    fi
else
    echo "   ⚠️  未找到日志目录"
fi

# 3. 检查系统资源
echo ""
echo "🔍 3. 检查系统资源..."

# 检查网络连接
if command -v netstat &> /dev/null; then
    SFTP_CONNECTIONS=$(netstat -an 2>/dev/null | grep ":22" | grep ESTABLISHED | wc -l)
    echo "   当前SFTP连接数: $SFTP_CONNECTIONS"
    if [ "$SFTP_CONNECTIONS" -gt 10 ]; then
        echo "   ⚠️  SFTP连接数较多，可能存在连接泄漏"
    fi
else
    echo "   ⚠️  netstat命令不可用，无法检查网络连接"
fi

# 检查Java进程
if command -v jps &> /dev/null; then
    SEATUNNEL_PROCESSES=$(jps 2>/dev/null | grep -i seatunnel | wc -l)
    echo "   SeaTunnel进程数: $SEATUNNEL_PROCESSES"
    if [ "$SEATUNNEL_PROCESSES" -gt 1 ]; then
        echo "   ⚠️  发现多个SeaTunnel进程，可能需要清理"
    fi
fi

# 4. 配置建议
echo ""
echo "💡 4. 配置建议..."

echo "   推荐的连接池配置:"
echo "   hadoop_conf = {"
echo "     \"fs.sftp.connection.max\" = \"10\"  # 增加连接数"
echo "     \"fs.sftp.timeout\" = \"60000\""
echo "     \"fs.sftp.buffer.size\" = \"32768\""
echo "   }"
echo ""
echo "   推荐的并发配置:"
echo "   env {"
echo "     execution.parallelism = 1  # 先用低并发测试"
echo "     parallelism = 1"
echo "   }"

# 5. 故障排除步骤
echo ""
echo "🛠️  5. 故障排除步骤..."
echo "   1. 重启SeaTunnel服务清理连接池"
echo "   2. 使用单并发配置测试: test_sftp_fixed_config.conf"
echo "   3. 检查SFTP服务器连接限制"
echo "   4. 监控日志确认修复效果"
echo "   5. 逐步增加并发度"

# 6. 测试命令
echo ""
echo "🧪 6. 测试命令..."
echo "   # 基础功能测试"
echo "   ./bin/seatunnel.sh --config test_sftp_fixed_config.conf"
echo ""
echo "   # 查看实时日志"
echo "   tail -f logs/seatunnel-engine-server.log | grep -i sftp"

echo ""
echo "✅ 诊断完成!"
